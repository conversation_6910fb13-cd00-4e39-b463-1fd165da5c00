import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  Alert,
  ActivityIndicator,
  KeyboardAvoidingView,
  Platform,
  ScrollView,
} from 'react-native';
import { useAppDispatch, useAppSelector } from '../hooks/redux';
import {
  loginRequest,
  clearError,
  selectAuthError,
  selectAuthLoading,
  selectIsAuthenticated
} from '../redux/slices/authSlice';
import { shadows, radius, spacing, typography, buttonStyles, inputStyles, useTheme } from '../theme';

interface LoginProps {
  navigation: {
    navigate: () => void;
  };
}

const Login: React.FC<LoginProps> = ({ navigation }) => {
  const [username, setUsername] = useState('');
  const [password, setPassword] = useState('');

  const dispatch = useAppDispatch();
  const loading = useAppSelector(selectAuthLoading);
  const error = useAppSelector(selectAuthError);
  const isAuthenticated = useAppSelector(selectIsAuthenticated);
  const { colors } = useTheme();

  // Clear error when component mounts
  useEffect(() => {
    dispatch(clearError());
  }, [dispatch]);

  // Navigate to main app when authenticated
  useEffect(() => {
    if (isAuthenticated) {
      // Navigation will be handled by the main App component
    }
  }, [isAuthenticated]);

  // Show error alert
  useEffect(() => {
    if (error) {
      Alert.alert('Login Error', error, [
        { text: 'OK', onPress: () => dispatch(clearError()) }
      ]);
    }
  }, [error, dispatch]);

  const handleSubmit = () => {
    if (!username.trim() || !password.trim()) {
      Alert.alert('Error', 'Please enter both username and password');
      return;
    }

    // Dispatch login request with credentials directly as payload
    dispatch(loginRequest({
      username: username.trim(),
      password: password
    }));
  };

  const navigateToRegister = () => {
    navigation.navigate();
  };

  const styles = createStyles(colors);

  return (
    <KeyboardAvoidingView 
      style={styles.container} 
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
    >
      <ScrollView contentContainerStyle={styles.scrollContainer}>
        <View style={styles.formContainer}>
          <Text style={styles.title}>Welcome Back</Text>
          <Text style={styles.subtitle}>Sign in to your account</Text>

          <View style={styles.inputContainer}>
            <Text style={styles.label}>Username</Text>
            <TextInput
              style={styles.input}
              value={username}
              onChangeText={setUsername}
              placeholder="Enter your username"
              autoCapitalize="none"
              autoCorrect={false}
              editable={!loading}
            />
          </View>

          <View style={styles.inputContainer}>
            <Text style={styles.label}>Password</Text>
            <TextInput
              style={styles.input}
              value={password}
              onChangeText={setPassword}
              placeholder="Enter your password"
              secureTextEntry
              autoCapitalize="none"
              autoCorrect={false}
              editable={!loading}
            />
          </View>

          <TouchableOpacity
            style={[styles.button, loading && styles.buttonDisabled]}
            onPress={handleSubmit}
            disabled={loading}
          >
            {loading ? (
              <ActivityIndicator color="#ffffff" />
            ) : (
              <Text style={styles.buttonText}>Login</Text>
            )}
          </TouchableOpacity>

          <View style={styles.linkContainer}>
            <Text style={styles.linkText}>
              Don't have an account?{' '}
              <Text style={styles.link} onPress={navigateToRegister}>
                Register
              </Text>
            </Text>
          </View>
        </View>
      </ScrollView>
    </KeyboardAvoidingView>
  );
};

const createStyles = (colors: any) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  scrollContainer: {
    flexGrow: 1,
    justifyContent: 'center',
    padding: spacing.lg,
  },
  formContainer: {
    backgroundColor: colors.cardBackground,
    padding: spacing.xl,
    borderRadius: radius.xl, // More rounded to match frontend (25px)
    ...shadows.md,
  },
  title: {
    ...typography.h1,
    textAlign: 'center',
    marginBottom: spacing.sm,
    color: colors.text,
  },
  subtitle: {
    ...typography.body,
    textAlign: 'center',
    marginBottom: spacing.xl,
    color: colors.textSecondary,
  },
  inputContainer: {
    marginBottom: spacing.lg,
  },
  label: {
    fontSize: 16,
    fontFamily: 'Outfit-Medium',
    marginBottom: spacing.sm,
    color: colors.text,
  },
  input: {
    borderWidth: 1.5, // Matching frontend border width
    borderColor: colors.inputBorder,
    borderRadius: radius.sm, // 8px to match frontend
    padding: spacing.md,
    fontSize: 16,
    fontFamily: 'Outfit-Regular',
    backgroundColor: colors.cardBackground,
    color: colors.text,
    minHeight: 50, // Matching frontend height
  },
  button: {
    backgroundColor: colors.primary, // Using theme primary color
    padding: spacing.md,
    borderRadius: radius.xl, // 45px equivalent to match frontend
    alignItems: 'center',
    marginTop: spacing.md,
    minHeight: 50, // Matching frontend height
  },
  buttonDisabled: {
    backgroundColor: colors.gray300,
  },
  buttonText: {
    color: colors.white,
    fontSize: 16, // Matching frontend font size
    fontFamily: 'Outfit-Medium', // Matching frontend font weight
  },
  linkContainer: {
    marginTop: spacing.lg,
    alignItems: 'center',
  },
  linkText: {
    fontSize: 16,
    fontFamily: 'Outfit-Regular',
    color: colors.textSecondary,
  },
  link: {
    color: colors.primary, // Using theme primary color to match frontend
    fontFamily: 'Outfit-Medium', // Matching frontend font weight
  },
});

export default Login;
