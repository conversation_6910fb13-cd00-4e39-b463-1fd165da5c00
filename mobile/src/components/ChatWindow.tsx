import React, { useState, useEffect, useRef, useMemo } from 'react';
import {
  View,
  Text,
  FlatList,
  TouchableOpacity,
  StyleSheet,
  SafeAreaView,
} from 'react-native';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { useAppSelector, useAppDispatch } from '../hooks/redux';
import { selectConnected, sendMessageRequest, connectRequest } from '../redux/slices/socketSlice';
import { selectAuthUser, selectIsAuthenticated, selectAuthToken } from '../redux/slices/authSlice';
import { selectIsUserTyping } from '../redux/slices/typingSlice';
import { selectUserEmojiReaction } from '../redux/slices/emojiReactionSlice';
import { clearCurrentReceiver, selectCurrentReceiverUsername } from '../redux/slices/chatDBSlice';
import { ChatRoomNavigationProp } from '../navigation/types';
import UserInfo from './UserInfo';
import ClearChatModal from './ClearChatModal';
import DeleteUserModal from './DeleteUserModal';
import MessageInput from './MessageInput';
import MessageItem from './MessageItem';
import { useTheme, typography } from '../theme';

interface Message {
  id: string;
  room_id: string;
  sender_username: string;
  receiver_username: string;
  message: string;
  type?: 'text' | 'clear_chat' | 'typing' | 'delete_user' | 'emoji_reaction';
  timestamp: number;
  status: string;
  is_mine: boolean;
}

type MessageRecord = Record<string, any> | Message;

interface ChatWindowProps {
  messages: MessageRecord[];
  receiverUsername: string | null;
  onClearChat?: () => void;
  onBackToRooms?: () => void;
  onShowProfile?: () => void;
  navigation?: ChatRoomNavigationProp;
}

const ChatWindow: React.FC<ChatWindowProps> = ({
  messages = [],
  receiverUsername = null,
  onClearChat,
  onBackToRooms,
  onShowProfile,
  navigation
}) => {
  const dispatch = useAppDispatch();
  const connected = useAppSelector(selectConnected);
  const currentUser = useAppSelector(selectAuthUser);
  const selectedReceiverUsername = useAppSelector(selectCurrentReceiverUsername);
  const { colors } = useTheme();
  const flatListRef = useRef<FlatList>(null);

  const [showClearModal, setShowClearModal] = useState<boolean>(false);
  const [clearingChat, setClearingChat] = useState<boolean>(false);
  const [showDeleteUserModal, setShowDeleteUserModal] = useState<boolean>(false);
  const [deletingUser, setDeletingUser] = useState<boolean>(false);
  const [chatCleared, setChatCleared] = useState<boolean>(false);
  const [isNearBottom, setIsNearBottom] = useState<boolean>(true);
  const [showScrollToBottom, setShowScrollToBottom] = useState<boolean>(false);
  const [previousMessageCount, setPreviousMessageCount] = useState<number>(0);

  const isAuthenticated = useAppSelector(selectIsAuthenticated);
  const authToken = useAppSelector(selectAuthToken);

  // Memoize selector functions to prevent infinite rerenders
  const typingSelector = useMemo(() =>
    receiverUsername ? selectIsUserTyping(receiverUsername) : () => false,
    [receiverUsername]
  );

  const emojiReactionSelector = useMemo(() =>
    receiverUsername ? selectUserEmojiReaction(receiverUsername) : () => null,
    [receiverUsername]
  );

  const myEmojiReactionSelector = useMemo(() =>
    currentUser ? selectUserEmojiReaction(currentUser) : () => null,
    [currentUser]
  );

  // Get typing status from Redux
  const isTyping = useAppSelector(typingSelector);

  // Get emoji reaction from Redux (for received reactions)
  const emojiReaction = useAppSelector(emojiReactionSelector);

  // Get our own emoji reaction (for sent reactions)
  const myEmojiReaction = useAppSelector(myEmojiReactionSelector);

  // Format timestamp if available
  const formatTime = (timestamp: number): string => {
    if (!timestamp) return '';

    try {
      const date = new Date(timestamp);
      return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
    } catch (error) {
      return '';
    }
  };

  // Group messages by date
  const getMessageDate = (timestamp: number): string => {
    if (!timestamp) return '';

    try {
      const date = new Date(timestamp);
      const today = new Date();
      const yesterday = new Date(today);
      yesterday.setDate(yesterday.getDate() - 1);

      if (date.toDateString() === today.toDateString()) {
        return 'Today';
      } else if (date.toDateString() === yesterday.toDateString()) {
        return 'Yesterday';
      } else {
        return date.toLocaleDateString(undefined, {
          weekday: 'long',
          month: 'short',
          day: 'numeric'
        });
      }
    } catch (error) {
      return '';
    }
  };

  // Handle opening the clear chat modal
  const handleOpenClearModal = () => {
    setShowClearModal(true);
  };

  // Handle closing the clear chat modal
  const handleCloseClearModal = () => {
    setShowClearModal(false);
  };

  // Handle clearing the chat
  const handleClearChat = async () => {
    if (!currentUser || !receiverUsername) return;

    try {
      setClearingChat(true);

      // First, send a clear_chat type message to notify the other user to clear their chat
      dispatch(sendMessageRequest({
        receiverUsername,
        messageText: 'Chat cleared',
        messageType: 'clear_chat'
      }));

      // Set chat cleared state to true
      setChatCleared(true);

      // Call the parent component's onClearChat callback if provided
      if (onClearChat) {
        onClearChat();
      }

      setShowClearModal(false);
    } catch (error) {
      console.error('Failed to clear chat:', error);
    } finally {
      setClearingChat(false);
    }
  };

  // Handle opening the delete user modal
  const handleOpenDeleteUserModal = () => {
    setShowDeleteUserModal(true);
  };

  // Handle closing the delete user modal
  const handleCloseDeleteUserModal = () => {
    setShowDeleteUserModal(false);
  };

  // Handle deleting the user
  const handleDeleteUser = async () => {
    if (!currentUser || !receiverUsername) return;

    try {
      setDeletingUser(true);

      // Send a delete_user type message to notify the other user to delete the room
      dispatch(sendMessageRequest({
        receiverUsername,
        messageText: 'User deleted',
        messageType: 'delete_user'
      }));

      // Clear the current receiver to close the chat window
      dispatch(clearCurrentReceiver());

      setShowDeleteUserModal(false);

      // Navigate back to MainScreen
      if (navigation) {
        navigation.navigate('MainScreen');
      }
    } catch (error) {
      console.error('Failed to delete user:', error);
    } finally {
      setDeletingUser(false);
    }
  };

  const chatStatus = connected
    ? (isTyping ? 'Typing' : 'Online')
    : 'Offline';

  // Auto-reconnect mechanism
  useEffect(() => {
    let reconnectTimer: NodeJS.Timeout | null = null;

    // If authenticated but not connected, try to reconnect
    if (isAuthenticated && authToken && !connected) {
      console.log('Socket disconnected, attempting to reconnect...');
      reconnectTimer = setTimeout(() => {
        console.log('Reconnecting to socket...');
        dispatch(connectRequest({ authToken }));
      }, 3000); // Try to reconnect after 3 seconds
    }

    // Clean up timer on unmount
    return () => {
      if (reconnectTimer) {
        clearTimeout(reconnectTimer);
      }
    };
  }, [isAuthenticated, authToken, connected, dispatch]);

  const handleUserClick = () => {
    if (onShowProfile) {
      onShowProfile();
    }
  };

  // Handle scroll events to detect user's scroll position
  const handleScroll = (event: any) => {
    const { contentOffset, contentSize, layoutMeasurement } = event.nativeEvent;
    const scrollPosition = contentOffset.y;
    const scrollViewHeight = layoutMeasurement.height;
    const contentHeight = contentSize.height;

    // Calculate if user is near the bottom (within 100px threshold)
    const distanceFromBottom = contentHeight - scrollPosition - scrollViewHeight;
    const nearBottom = distanceFromBottom < 100;

    setIsNearBottom(nearBottom);
    setShowScrollToBottom(!nearBottom && messages.length > 0);
  };

  // Function to scroll to bottom manually
  const scrollToBottom = () => {
    flatListRef.current?.scrollToEnd({ animated: true });
    setShowScrollToBottom(false);
    setIsNearBottom(true);
  };

  // Smart scroll logic - only auto-scroll when appropriate
  useEffect(() => {
    if (messages.length > 0) {
      const messageCountChanged = messages.length !== previousMessageCount;
      const newMessagesAdded = messages.length > previousMessageCount;

      // Auto-scroll conditions:
      // 1. First load (previousMessageCount is 0)
      // 2. User is near bottom and new messages were added
      // 3. User sent a new message (last message is from current user)
      const shouldAutoScroll =
        previousMessageCount === 0 || // First load
        (isNearBottom && newMessagesAdded) || // User is at bottom and new messages arrived
        (newMessagesAdded && messages[messages.length - 1]?.is_mine); // User sent a message

      if (shouldAutoScroll) {
        setTimeout(() => {
          flatListRef.current?.scrollToEnd({ animated: true });
        }, 100);
        setIsNearBottom(true);
        setShowScrollToBottom(false);
      }

      // Update previous message count
      if (messageCountChanged) {
        setPreviousMessageCount(messages.length);
      }

      // Reset chatCleared state if new messages arrive
      setChatCleared(false);
    }
  }, [messages, previousMessageCount, isNearBottom, currentUser]);

  const renderMessage = ({ item, index }: { item: MessageRecord; index: number }) => {
    if (!item) return null;

    const messageTimestamp = item.timestamp || Date.now();
    const nextMessage = messages[index + 1];
    const isLastInGroup =
      !nextMessage ||
      nextMessage.is_mine !== item.is_mine ||
      getMessageDate(messageTimestamp) !== getMessageDate(nextMessage?.timestamp || 0);

    return (
      <MessageItem
        message={item}
        formatTime={formatTime}
        isLastInGroup={isLastInGroup}
      />
    );
  };

  const styles = createStyles(colors);

  return (
    <SafeAreaView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        {receiverUsername && (
          <>
            {onBackToRooms && (
              <TouchableOpacity style={styles.backButton} onPress={onBackToRooms}>
                <Icon name="arrow-back" size={24} color={colors.primary} />
              </TouchableOpacity>
            )}
            <UserInfo
              userId={receiverUsername}
              status={chatStatus}
              disableEmoji={true}
              onClick={handleUserClick}
              style={styles.userInfo}
            />
            <View style={styles.headerActions}>
              <TouchableOpacity
                style={styles.actionButton}
                onPress={handleOpenClearModal}
              >
                <Icon name="refresh" size={20} color={colors.text} />
              </TouchableOpacity>
              <TouchableOpacity
                style={styles.actionButton}
                onPress={handleOpenDeleteUserModal}
              >
                <Icon name="delete" size={20} color={colors.text} />
              </TouchableOpacity>
            </View>
          </>
        )}
      </View>

      {/* Connection status banner */}
      {isAuthenticated && !connected && (
        <View style={styles.connectionBanner}>
          <Text style={styles.connectionText}>Reconnecting...</Text>
        </View>
      )}

      {/* Messages */}
      <View style={styles.messagesContainer}>
        {messages.length > 0 ? (
          <FlatList
            ref={flatListRef}
            data={messages}
            renderItem={renderMessage}
            keyExtractor={(item, index) => item.id || `msg-${index}`}
            style={styles.messagesList}
            showsVerticalScrollIndicator={false}
            onScroll={handleScroll}
            scrollEventThrottle={16}
          />
        ) : (
          <View style={styles.noMessages}>
            <Text style={styles.noMessagesText}>
              {!receiverUsername
                ? 'Select a contact to view messages'
                : !connected
                  ? 'Connect to a server to receive messages.'
                  : chatCleared
                    ? 'Chat has been cleared'
                    : 'No messages yet. Start the conversation!'}
            </Text>
          </View>
        )}

        {/* Emoji reactions */}
        {emojiReaction?.emoji && (
          <View style={styles.emojiReactionContainer}>
            <Text style={styles.emojiReaction}>{emojiReaction.emoji}</Text>
          </View>
        )}

        {myEmojiReaction?.emoji && (
          <View style={[styles.emojiReactionContainer, styles.myEmojiReaction]}>
            <Text style={styles.emojiReaction}>{myEmojiReaction.emoji}</Text>
          </View>
        )}

        {/* Scroll to bottom button */}
        {showScrollToBottom && (
          <TouchableOpacity style={styles.scrollToBottomButton} onPress={scrollToBottom}>
            <Icon name="keyboard-arrow-down" size={24} color={colors.white} />
          </TouchableOpacity>
        )}
      </View>

      {/* Message Input */}
      <MessageInput receiverUsername={selectedReceiverUsername || ''} />

      {/* Modals */}
      <ClearChatModal
        isVisible={showClearModal}
        onClose={handleCloseClearModal}
        onConfirm={handleClearChat}
        loading={clearingChat}
      />

      <DeleteUserModal
        isVisible={showDeleteUserModal}
        onClose={handleCloseDeleteUserModal}
        onConfirm={handleDeleteUser}
        loading={deletingUser}
        username={receiverUsername || undefined}
      />
    </SafeAreaView>
  );
};

const createStyles = (colors: any) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.cardBackground,
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
  },
  backButton: {
    marginRight: 12,
    padding: 8,
  },

  userInfo: {
    flex: 1,
  },
  headerActions: {
    flexDirection: 'row',
  },
  actionButton: {
    marginLeft: 12,
    padding: 8,
  },

  connectionBanner: {
    backgroundColor: colors.warning,
    paddingVertical: 8,
    alignItems: 'center',
  },
  connectionText: {
    color: colors.white,
    fontSize: 14,
    fontFamily: 'Outfit-Medium',
  },
  messagesContainer: {
    flex: 1,
  },
  messagesList: {
    flex: 1,
    paddingVertical: 8,
  },
  noMessages: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 32,
  },
  noMessagesText: {
    fontSize: 16,
    fontFamily: 'Outfit-Regular',
    color: colors.textSecondary,
    textAlign: 'center',
    lineHeight: 24,
  },
  emojiReactionContainer: {
    alignItems: 'flex-start',
    marginHorizontal: 16,
    marginVertical: 4,
  },
  myEmojiReaction: {
    alignItems: 'flex-end',
  },
  emojiReaction: {
    fontSize: 24,
    backgroundColor: colors.cardBackground,
    borderRadius: 20,
    padding: 8,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.2,
    shadowRadius: 2,
    elevation: 2,
  },
  scrollToBottomButton: {
    position: 'absolute',
    bottom: 20,
    right: 20,
    backgroundColor: colors.primary,
    borderRadius: 25,
    width: 50,
    height: 50,
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },

});

export default ChatWindow;
