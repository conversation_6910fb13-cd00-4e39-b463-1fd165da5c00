/**
 * Main theme export file
 * Provides utilities and constants for consistent styling across the mobile app
 */

export * from './colors';
export * from './imageStyles';
export * from '../context/ThemeContext';

import { colors, shadows, radius, spacing } from './colors';
import { StyleSheet } from 'react-native';

// Common button styles matching frontend with Outfit font
export const buttonStyles = StyleSheet.create({
  primary: {
    backgroundColor: colors.primary,
    borderRadius: radius.lg,
    paddingVertical: spacing.md,
    paddingHorizontal: spacing.lg,
    alignItems: 'center',
    justifyContent: 'center',
    minHeight: 44,
    ...shadows.md,
  },
  primaryText: {
    color: colors.white,
    fontSize: 16,
    fontWeight: '500',
    fontFamily: 'Outfit',
  },
  secondary: {
    backgroundColor: colors.buttonSecondaryBg,
    borderRadius: radius.lg,
    paddingVertical: spacing.md,
    paddingHorizontal: spacing.lg,
    alignItems: 'center',
    justifyContent: 'center',
    minHeight: 44,
    borderWidth: 1,
    borderColor: colors.buttonSecondaryBorder,
  },
  secondaryText: {
    color: colors.buttonSecondaryText,
    fontSize: 16,
    fontWeight: '500',
    fontFamily: 'Outfit',
  },
  danger: {
    backgroundColor: colors.danger,
    borderRadius: radius.lg,
    paddingVertical: spacing.md,
    paddingHorizontal: spacing.lg,
    alignItems: 'center',
    justifyContent: 'center',
    minHeight: 44,
    ...shadows.md,
  },
  dangerText: {
    color: colors.white,
    fontSize: 16,
    fontWeight: '500',
    fontFamily: 'Outfit',
  },
  disabled: {
    backgroundColor: colors.gray300,
    borderRadius: radius.lg,
    paddingVertical: spacing.md,
    paddingHorizontal: spacing.lg,
    alignItems: 'center',
    justifyContent: 'center',
    minHeight: 44,
  },
  disabledText: {
    color: colors.gray500,
    fontSize: 16,
    fontWeight: '500',
    fontFamily: 'Outfit',
  },
});

// Common input styles matching frontend with Outfit font
export const inputStyles = StyleSheet.create({
  container: {
    marginBottom: spacing.md,
  },
  input: {
    borderWidth: 1,
    borderColor: colors.inputBorder,
    borderRadius: radius.lg, // Using lg radius to match frontend (45px equivalent)
    paddingVertical: spacing.md,
    paddingHorizontal: spacing.lg,
    fontSize: 16,
    fontWeight: '400',
    fontFamily: 'Outfit',
    backgroundColor: colors.cardBackground,
    color: colors.text,
    minHeight: 50, // Matching frontend height
  },
  inputFocused: {
    borderColor: colors.primary,
    ...shadows.sm,
  },
  label: {
    fontSize: 14,
    fontWeight: '500',
    fontFamily: 'Outfit',
    color: colors.text,
    marginBottom: spacing.sm,
  },
  error: {
    fontSize: 12,
    fontWeight: '400',
    fontFamily: 'Outfit',
    color: colors.danger,
    marginTop: spacing.xs,
  },
});

// Common card styles with Outfit font
export const cardStyles = StyleSheet.create({
  container: {
    backgroundColor: colors.cardBackground,
    borderRadius: radius.md,
    padding: spacing.lg,
    ...shadows.sm,
  },
  header: {
    backgroundColor: colors.primary,
    borderTopLeftRadius: radius.md,
    borderTopRightRadius: radius.md,
    padding: spacing.lg,
  },
  headerText: {
    color: colors.white,
    fontSize: 18,
    fontWeight: '500',
    fontFamily: 'Outfit',
  },
  content: {
    padding: spacing.lg,
  },
});

// Typography styles matching frontend with Outfit font
export const typography = StyleSheet.create({
  h1: {
    fontSize: 28, // 1.75rem equivalent
    fontWeight: '700',
    fontFamily: 'Outfit',
    color: colors.text,
    lineHeight: 34,
  },
  h2: {
    fontSize: 22, // 1.35rem equivalent
    fontWeight: '700',
    fontFamily: 'Outfit',
    color: colors.text,
    lineHeight: 26,
  },
  h3: {
    fontSize: 18,
    fontWeight: '500',
    fontFamily: 'Outfit',
    color: colors.text,
    lineHeight: 22,
  },
  body: {
    fontSize: 16,
    fontWeight: '400',
    fontFamily: 'Outfit',
    color: colors.text,
    lineHeight: 24,
  },
  bodySecondary: {
    fontSize: 16,
    fontWeight: '400',
    fontFamily: 'Outfit',
    color: colors.textSecondary,
    lineHeight: 24,
  },
  caption: {
    fontSize: 12,
    fontWeight: '400',
    fontFamily: 'Outfit',
    color: colors.textSecondary,
    lineHeight: 16,
  },
  link: {
    fontSize: 16,
    fontWeight: '400',
    fontFamily: 'Outfit',
    color: colors.primary,
    textDecorationLine: 'underline',
  },
});

// Layout utilities
export const layout = {
  flex: {
    flex: 1,
  },
  center: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  row: {
    flexDirection: 'row' as const,
  },
  column: {
    flexDirection: 'column' as const,
  },
  spaceBetween: {
    justifyContent: 'space-between',
  },
  spaceAround: {
    justifyContent: 'space-around',
  },
  alignCenter: {
    alignItems: 'center',
  },
  alignStart: {
    alignItems: 'flex-start',
  },
  alignEnd: {
    alignItems: 'flex-end',
  },
} as const;

// Export theme object for easy access
export const theme = {
  colors,
  shadows,
  radius,
  spacing,
  buttonStyles,
  inputStyles,
  cardStyles,
  typography,
  layout,
} as const;

export default theme;
